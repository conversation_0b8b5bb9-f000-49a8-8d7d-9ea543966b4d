Write-Host "=== KIỂM TRA DỮ LIỆU POWERSYNC ===" -ForegroundColor Green

Write-Host "`n1. <PERSON><PERSON><PERSON> tra dữ liệu trong PostgreSQL (Source Database):" -ForegroundColor Yellow
Write-Host "Tables trong database:" -ForegroundColor Cyan
cd powersync-service
docker-compose exec postgres psql -U postgres -d postgres -c "\dt"

Write-Host "`nDữ liệu trong bảng 'lists':" -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT * FROM lists;"

Write-Host "`nDữ liệu trong bảng 'todos':" -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT * FROM todos;"

Write-Host "`n2. Kiểm tra PowerSync Publication:" -ForegroundColor Yellow
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT * FROM pg_publication;"

Write-Host "`n3. <PERSON><PERSON>m tra dữ liệu trong MongoDB (PowerSync Storage):" -ForegroundColor Yellow
Write-Host "Connecting to MongoDB..." -ForegroundColor Cyan
docker-compose exec mongo mongosh --eval "
use powersync_demo;
print('Collections:');
db.getCollectionNames();
print('\\nBucket data:');
db.buckets.find().limit(5);
"

Write-Host "`n4. Kiểm tra PowerSync Logs (5 dòng cuối):" -ForegroundColor Yellow
docker-compose logs powersync --tail=5

cd ..

Write-Host "`n=== HƯỚNG DẪN THÊM DỮ LIỆU ===" -ForegroundColor Green
Write-Host "Để thêm list mới:" -ForegroundColor Yellow
Write-Host '  docker-compose exec postgres psql -U postgres -d postgres -c "INSERT INTO lists (name) VALUES ('"'"'New List'"'"');"'

Write-Host "`nĐể thêm todo mới:" -ForegroundColor Yellow
Write-Host '  docker-compose exec postgres psql -U postgres -d postgres -c "INSERT INTO todos (list_id, description) VALUES (1, '"'"'New Task'"'"');"'

Write-Host "`nĐể xem dữ liệu real-time:" -ForegroundColor Yellow
Write-Host "  docker-compose logs powersync -f"
