Write-Host "=== POWERSYNC SERVICES STATUS ===" -ForegroundColor Green

Write-Host "1. Node.js Backend:" -ForegroundColor Yellow
curl http://localhost:6060/

Write-Host "2. PowerSync Service (404 is normal):" -ForegroundColor Yellow
curl http://localhost:8080/

Write-Host "3. Docker Containers:" -ForegroundColor Yellow
cd powersync-service
docker-compose ps
cd ..

Write-Host "4. Database Check:" -ForegroundColor Yellow
cd powersync-service
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT COUNT(*) FROM lists;"
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT COUNT(*) FROM todos;"
cd ..

Write-Host "=== CONFIGURATION ===" -ForegroundColor Green
Write-Host "PowerSync Service: http://localhost:8080"
Write-Host "Node.js Backend: http://localhost:6060"
Write-Host "PostgreSQL: localhost:5432"
Write-Host "MongoDB: localhost:27017"
Write-Host ""
Write-Host "Android App connects to:"
Write-Host "  PowerSync: http://********:8080"
Write-Host "  Backend: http://********:6060"
