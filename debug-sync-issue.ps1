Write-Host "=== DEBUG POWERSYNC SYNC ISSUE ===" -ForegroundColor Green

Write-Host "`n1. Ki<PERSON>m tra PowerSync service status:" -ForegroundColor Yellow
cd powersync-service
docker-compose ps powersync

Write-Host "`n2. <PERSON><PERSON><PERSON> tra PowerSync logs (10 dòng cuối):" -ForegroundColor Yellow
docker-compose logs powersync --tail=10

Write-Host "`n3. Kiểm tra có client connections không:" -ForegroundColor Yellow
Write-Host "Checking for active connections..." -ForegroundColor Cyan
docker-compose exec powersync netstat -an | findstr :8080

Write-Host "`n4. <PERSON>ểm tra dữ liệu trong MongoDB:" -ForegroundColor Yellow
docker-compose exec mongo mongosh --eval "
use powersync_demo;
print('=== Collections ===');
db.getCollectionNames();
print('=== Buckets count ===');
db.buckets.countDocuments();
print('=== Sample bucket data ===');
db.buckets.find().limit(2);
"

Write-Host "`n5. <PERSON><PERSON><PERSON> tra PostgreSQL replication:" -ForegroundColor Yellow
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT * FROM pg_replication_slots;"

Write-Host "`n6. Test thêm dữ liệu mới:" -ForegroundColor Yellow
Write-Host "Adding new test data..." -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "INSERT INTO lists (name) VALUES ('Debug Test List');"

Write-Host "`nChecking PowerSync logs after insert:" -ForegroundColor Cyan
Start-Sleep -Seconds 2
docker-compose logs powersync --tail=3

Write-Host "`n7. Kiểm tra MongoDB sau khi thêm dữ liệu:" -ForegroundColor Yellow
docker-compose exec mongo mongosh --eval "
use powersync_demo;
print('=== Latest bucket data ===');
db.buckets.find().sort({_id: -1}).limit(1);
"

cd ..

Write-Host "`n=== CHẨN ĐOÁN ===" -ForegroundColor Green
Write-Host "Nếu bạn thấy:" -ForegroundColor Yellow
Write-Host "✓ PowerSync logs có 'Flushed X updates' => Sync từ PostgreSQL hoạt động" -ForegroundColor Green
Write-Host "✓ MongoDB có dữ liệu => PowerSync storage hoạt động" -ForegroundColor Green
Write-Host "✗ Không có client connections => Android app chưa kết nối" -ForegroundColor Red

Write-Host "`nĐể Android app kết nối được:" -ForegroundColor Yellow
Write-Host "1. Build và chạy Android app trên emulator" -ForegroundColor Cyan
Write-Host "2. App sẽ tự động kết nối đến http://********:8080" -ForegroundColor Cyan
Write-Host "3. Kiểm tra logs Android app để xem có lỗi kết nối không" -ForegroundColor Cyan
