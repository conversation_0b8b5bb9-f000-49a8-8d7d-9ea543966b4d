Write-Host "=== TEST REAL-TIME SYNC POWERSYNC ===" -ForegroundColor Green

Write-Host "`nBước 1: Xem dữ liệu hiện tại" -ForegroundColor Yellow
cd powersync-service
Write-Host "Lists hiện tại:" -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT COUNT(*) as total_lists FROM lists;"

Write-Host "`nTodos hiện tại:" -ForegroundColor <PERSON><PERSON>  
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT COUNT(*) as total_todos FROM todos;"

Write-Host "`nBước 2: Thêm dữ liệu mới" -ForegroundColor Yellow
$timestamp = Get-Date -Format "HH:mm:ss"
Write-Host "Thêm list mới lúc $timestamp..." -ForegroundColor <PERSON><PERSON>
docker-compose exec postgres psql -U postgres -d postgres -c "INSERT INTO lists (name) VALUES ('Real-time Test $timestamp');"

Write-Host "Thêm todo mới..." -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "INSERT INTO todos (list_id, description) VALUES ((SELECT MAX(id) FROM lists), 'Task created at $timestamp');"

Write-Host "`nBước 3: Kiểm tra PowerSync logs" -ForegroundColor Yellow
Write-Host "PowerSync replication logs (3 dòng cuối):" -ForegroundColor Cyan
docker-compose logs powersync --tail=3

Write-Host "`nBước 4: Xem dữ liệu sau khi thêm" -ForegroundColor Yellow
Write-Host "Lists sau khi thêm:" -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT id, name, created_at FROM lists ORDER BY id DESC LIMIT 3;"

Write-Host "`nTodos sau khi thêm:" -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT id, list_id, description, created_at FROM todos ORDER BY id DESC LIMIT 3;"

Write-Host "`nBước 5: Test update operation" -ForegroundColor Yellow
Write-Host "Cập nhật todo cuối cùng thành completed..." -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "UPDATE todos SET completed = true WHERE id = (SELECT MAX(id) FROM todos);"

Write-Host "`nKiểm tra logs sau update:" -ForegroundColor Cyan
docker-compose logs powersync --tail=2

Write-Host "`nXem todo đã được update:" -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT id, description, completed FROM todos WHERE id = (SELECT MAX(id) FROM todos);"

cd ..

Write-Host "`n=== KET QUA ===" -ForegroundColor Green
Write-Host "OK Neu ban thay logs PowerSync voi 'Flushed X updates' => Real-time sync hoat dong!" -ForegroundColor Green
Write-Host "OK Du lieu duoc dong bo tu PostgreSQL sang PowerSync storage ngay lap tuc" -ForegroundColor Green
Write-Host "OK Android app se nhan duoc updates nay qua WebSocket connection" -ForegroundColor Green
