replication:
  connections:
    - type: postgresql
      uri: ********************************************/postgres

      # SSL settings
      sslmode: disable # 'verify-full' (default) or 'verify-ca' or 'disable'

# Connection settings for sync bucket storage
storage:
  type: mongodb
  uri: mongodb://mongo:27017/powersync_demo

# The port which the PowerSync API server will listen on
port: 8080

# Specify sync rules
sync_rules:
  # TODO use specific sync rules here
  content: |
    bucket_definitions:
      global:
        data:
          - SELECT id::text as id, name, created_at FROM lists
          - SELECT id::text as id, list_id, description, completed, created_at FROM todos

# Settings for client authentication
client_auth:
  # Enable this if using Supabase Auth
  supabase: false

  # JWKS URIs can be specified here.
  # jwks_uri: [TODO]

  # JWKS audience
  jwks:
    keys:
      - kty: 'oct'
        k: 'dGVzdGtleQ' # base64 for 'testkey'
        alg: 'HS256'
        kid: 'dev'
        
  audience: ['powersync-dev', 'powersync']

# Settings for telemetry reporting
# See https://docs.powersync.com/self-hosting/telemetry
telemetry:
  # Opt out of reporting anonymized usage metrics to PowerSync telemetry service
  disable_telemetry_sharing: false