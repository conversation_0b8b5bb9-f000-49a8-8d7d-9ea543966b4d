Write-Host "=== DEBUG POWERSYNC ===" -ForegroundColor Green

Write-Host "1. PowerSync Status:" -ForegroundColor Yellow
cd powersync-service
docker-compose ps powersync

Write-Host "2. Recent PowerSync Logs:" -ForegroundColor Yellow
docker-compose logs powersync --tail=5

Write-Host "3. MongoDB Data:" -ForegroundColor Yellow
docker-compose exec mongo mongosh --eval "use powersync_demo; db.buckets.countDocuments();"

Write-Host "4. Test Insert:" -ForegroundColor Yellow
docker-compose exec postgres psql -U postgres -d postgres -c "INSERT INTO lists (name) VALUES ('Test $(Get-Date -Format HH:mm:ss)');"

Write-Host "5. Check Logs After Insert:" -ForegroundColor Yellow
Start-Sleep -Seconds 2
docker-compose logs powersync --tail=2

cd ..

Write-Host "=== DIAGNOSIS ===" -ForegroundColor Green
Write-Host "If you see 'Flushed X updates' => PowerSync is working" -ForegroundColor Cyan
Write-Host "The issue is likely that Android app is not connected yet" -ForegroundColor Yellow
Write-Host "Build and run the Android app to see real sync in action" -ForegroundColor Cyan
