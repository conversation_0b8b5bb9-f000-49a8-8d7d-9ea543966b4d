// Script để tạo JWT token hợp lệ cho PowerSync
const crypto = require('crypto');

// <PERSON><PERSON>u hình từ PowerSync config
const secret = 'testkey'; // base64 decode của 'dGVzdGtleQ'
const issuer = 'powersync-dev';
const audience = ['powersync-dev', 'powersync'];

// Tạo header
const header = {
    "alg": "HS256",
    "typ": "JWT",
    "kid": "dev"
};

// Tạo payload
const payload = {
    "sub": "user123",
    "iat": Math.floor(Date.now() / 1000),
    "exp": Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
    "aud": audience,
    "iss": issuer
};

// Encode base64url
function base64urlEncode(str) {
    return Buffer.from(str)
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}

// Tạo JWT
const encodedHeader = base64urlEncode(JSON.stringify(header));
const encodedPayload = base64urlEncode(JSON.stringify(payload));
const data = `${encodedHeader}.${encodedPayload}`;

// Tạo signature
const signature = crypto
    .createHmac('sha256', secret)
    .update(data)
    .digest('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

const jwt = `${data}.${signature}`;

console.log('=== JWT TOKEN GENERATED ===');
console.log('Token:', jwt);
console.log('\n=== PAYLOAD ===');
console.log(JSON.stringify(payload, null, 2));
console.log('\n=== USAGE ===');
console.log('Update PowerSyncConfig.kt:');
console.log(`const val DEMO_JWT_TOKEN = "${jwt}"`);
