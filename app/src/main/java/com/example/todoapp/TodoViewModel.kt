package com.example.todoapp

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalCoroutinesApi::class)
class TodoViewModel(private val repository: TodoRepository) : ViewModel() {
    
    private val _uiState = MutableStateFlow(TodoUiState())
    val uiState: StateFlow<TodoUiState> = _uiState.asStateFlow()
    
    private val _currentListId = MutableStateFlow<String?>(null)

    val todos: StateFlow<List<Todo>> = _currentListId
        .filterNotNull()
        .flatMapLatest { listId ->
            repository.watchTodosByListId(listId)
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    init {
        initializeApp()
    }
    
    private fun initializeApp() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
                val listId = repository.createDefaultListIfNeeded()
                _currentListId.value = listId
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Lỗi khởi tạo: ${e.message}"
                )
            }
        }
    }
    
    fun addTodo(description: String) {
        val currentListId = _currentListId.value ?: return
        if (description.isBlank()) return
        
        viewModelScope.launch {
            try {
                repository.insertTodo(currentListId, description.trim())
                _uiState.value = _uiState.value.copy(inputText = "")
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi thêm công việc: ${e.message}"
                )
            }
        }
    }
    
    fun toggleTodoComplete(todoId: String, completed: Boolean) {
        viewModelScope.launch {
            try {
                repository.updateTodoCompleted(todoId, completed)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi cập nhật: ${e.message}"
                )
            }
        }
    }

    fun deleteTodo(todoId: String) {
        viewModelScope.launch {
            try {
                repository.deleteTodo(todoId)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "Lỗi xóa: ${e.message}"
                )
            }
        }
    }
    
    fun updateInputText(text: String) {
        _uiState.value = _uiState.value.copy(inputText = text)
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    fun retry() {
        initializeApp()
    }
}

data class TodoUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val inputText: String = ""
)
