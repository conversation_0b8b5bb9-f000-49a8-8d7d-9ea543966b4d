package com.example.todoapp

import android.os.Build
import androidx.annotation.RequiresApi
import com.powersync.PowerSyncDatabase
import kotlinx.coroutines.flow.Flow
import java.time.OffsetDateTime
import java.util.UUID

class TodoRepository(private val database: PowerSyncDatabase) {

    // Hàm tạo danh sách mặc định - sử dụng dữ liệu từ backend
    @RequiresApi(Build.VERSION_CODES.O)
    suspend fun createDefaultListIfNeeded(): Int {
        val existingLists = getAllLists()
        if (existingLists.isEmpty()) {
            // Không tạo mới, chỉ trả về ID của list đầu tiên từ backend
            // PowerSync sẽ tự động sync dữ liệu từ server
            return 1 // Default to first list
        }
        return existingLists.first().id
    }

    // Hàm thêm todo - sử dụng auto-increment ID
    @RequiresApi(Build.VERSION_CODES.O)
    suspend fun insertTodo(listId: Int, description: String, completed: Int = 0) {
        val createdAt = OffsetDateTime.now().toString()

        database.writeTransaction { tx ->
            tx.execute(
                sql = "INSERT INTO todos (list_id, description, completed, created_at) VALUES (?, ?, ?, ?)",
                parameters = listOf(listId, description, completed, createdAt)
            )
        }
    }

    // Hàm cập nhật mô tả todo
    suspend fun updateTodoDescription(id: Int, newDescription: String) {
        database.execute(
            sql = "UPDATE todos SET description = ? WHERE id = ?",
            parameters = listOf(newDescription, id)
        )
    }

    // Hàm cập nhật trạng thái hoàn thành
    suspend fun updateTodoCompleted(id: Int, completed: Int) {
        database.execute(
            sql = "UPDATE todos SET completed = ? WHERE id = ?",
            parameters = listOf(completed, id)
        )
    }

    // Hàm xóa todo
    suspend fun deleteTodo(id: Int) {
        database.execute(
            sql = "DELETE FROM todos WHERE id = ?",
            parameters = listOf(id)
        )
    }

    // Hàm lấy todo theo ID
    suspend fun getTodoById(id: Int): Todo? {
        return database.getOptional(
            sql = "SELECT id, list_id, description, completed, created_at FROM todos WHERE id = ?",
            parameters = listOf(id)
        ) { cursor ->
            val columnMap = cursor.columnNames
            Todo(
                id = (cursor.getLong(columnMap["id"] ?: 0) ?: 0).toInt(),
                listId = (cursor.getLong(columnMap["list_id"] ?: 1) ?: 0).toInt(),
                description = cursor.getString(columnMap["description"] ?: 2) ?: "",
                completed = (cursor.getLong(columnMap["completed"] ?: 3) ?: 0).toInt(),
                createdAt = cursor.getString(columnMap["created_at"] ?: 4) ?: ""
            )
        }
    }

    // Hàm lấy tất cả todos theo list ID
    suspend fun getTodosByListId(listId: Int): List<Todo> {
        return database.getAll(
            sql = "SELECT id, list_id, description, completed, created_at FROM todos WHERE list_id = ? ORDER BY created_at DESC",
            parameters = listOf(listId)
        ) { cursor ->
            val columnMap = cursor.columnNames
            Todo(
                id = (cursor.getLong(columnMap["id"] ?: 0) ?: 0).toInt(),
                listId = (cursor.getLong(columnMap["list_id"] ?: 1) ?: 0).toInt(),
                description = cursor.getString(columnMap["description"] ?: 2) ?: "",
                completed = (cursor.getLong(columnMap["completed"] ?: 3) ?: 0).toInt(),
                createdAt = cursor.getString(columnMap["created_at"] ?: 4) ?: ""
            )
        }
    }

    // Hàm theo dõi todos theo list ID (reactive)
    fun watchTodosByListId(listId: Int): Flow<List<Todo>> {
        return database.watch(
            sql = "SELECT id, list_id, description, completed, created_at FROM todos WHERE list_id = ? ORDER BY created_at DESC",
            parameters = listOf(listId)
        ) { cursor ->
            val columnMap = cursor.columnNames
            Todo(
                id = (cursor.getLong(columnMap["id"] ?: 0) ?: 0).toInt(),
                listId = (cursor.getLong(columnMap["list_id"] ?: 1) ?: 0).toInt(),
                description = cursor.getString(columnMap["description"] ?: 2) ?: "",
                completed = (cursor.getLong(columnMap["completed"] ?: 3) ?: 0).toInt(),
                createdAt = cursor.getString(columnMap["created_at"] ?: 4) ?: ""
            )
        }
    }

    // Hàm lấy tất cả danh sách
    suspend fun getAllLists(): List<TodoList> {
        return database.getAll(
            sql = "SELECT id, created_at, name, owner_id FROM lists ORDER BY created_at DESC"
        ) { cursor ->
            val columnMap = cursor.columnNames
            TodoList(
                id = cursor.getString(columnMap["id"] ?: 0) ?: "",
                createdAt = cursor.getString(columnMap["created_at"] ?: 1) ?: "",
                name = cursor.getString(columnMap["name"] ?: 2) ?: "",
                ownerId = cursor.getString(columnMap["owner_id"] ?: 3) ?: ""
            )
        }
    }

    // Hàm tạo danh sách mới
    @RequiresApi(Build.VERSION_CODES.O)
    suspend fun createList(name: String, ownerId: String = "default-user"): String {
        val id = UUID.randomUUID().toString()
        val createdAt = OffsetDateTime.now().toString()
        
        database.writeTransaction { tx ->
            tx.execute(
                sql = "INSERT INTO lists (id, created_at, name, owner_id) VALUES (?, ?, ?, ?)",
                parameters = listOf(id, createdAt, name, ownerId)
            )
        }
        return id
    }
}
