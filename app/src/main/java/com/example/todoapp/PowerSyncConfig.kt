package com.example.todoapp

/**
 * Configuration object cho PowerSync và Backend services
 */
object PowerSyncConfig {
    
    // Local development URLs
    // Sử dụng ******** cho Android emulator để truy cập localhost của host machine
    const val POWERSYNC_URL = "http://********:8080"
    const val BACKEND_URL = "http://********:6060"
    
    // JWT Configuration
    const val JWT_SECRET = "testkey"
    const val JWT_ISSUER = "powersync-dev"
    const val JWT_AUDIENCE = "powersync-dev"
    
    // Database Configuration
    const val DATABASE_NAME = "todoapp.db"
    
    // API Endpoints
    object Endpoints {
        const val AUTH = "$BACKEND_URL/auth"
        const val TODOS = "$BACKEND_URL/api/todos"
        const val LISTS = "$BACKEND_URL/api/lists"
        const val SYNC = "$BACKEND_URL/api/sync"
    }
    
    // JWT Token hợp lệ được tạo với secret key đúng
    // Trong production, token này sẽ được tạo động từ authentication service
    const val DEMO_JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImRldiJ9.eyJzdWIiOiJ1c2VyMTIzIiwiaWF0IjoxNzQ5NTUzMTg4LCJleHAiOjE3NDk2Mzk1ODgsImF1ZCI6WyJwb3dlcnN5bmMtZGV2IiwicG93ZXJzeW5jIl0sImlzcyI6InBvd2Vyc3luYy1kZXYifQ.ZduxpCaP3grTxRW0-v0aJfCknM4fDv1kO3TXqqQ6wOw"
}
