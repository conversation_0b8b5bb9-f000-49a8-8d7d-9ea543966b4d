package com.example.todoapp

import com.powersync.db.schema.*

val AppSchema = Schema(
    listOf(
        Table(
            name = "lists",
            columns = listOf(
                Column.integer("id"),
                Column.text("name"),
                Column.text("created_at")
            )
        ),
        Table(
            name = "todos",
            columns = listOf(
                Column.integer("id"),
                Column.integer("list_id"),
                Column.text("description"),
                Column.integer("completed"), // 0 = false, 1 = true
                Column.text("created_at")
            ),
            indexes = listOf(Index("list", listOf(IndexedColumn.descending("list_id"))))
        )
    )
)

// Định nghĩa lớp dữ liệu Todo - khớp với backend PostgreSQL
data class Todo(
    val id: Int,
    val listId: Int,
    val description: String,
    val completed: Int, // 0 = false, 1 = true
    val createdAt: String
)

// Định nghĩa lớp dữ liệu TodoList - khớp với backend PostgreSQL
data class TodoList(
    val id: Int,
    val name: String,
    val createdAt: String
)
