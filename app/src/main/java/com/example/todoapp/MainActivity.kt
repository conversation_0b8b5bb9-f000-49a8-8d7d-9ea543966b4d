package com.example.todoapp

import android.annotation.SuppressLint
import android.os.Build
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.todoapp.ui.theme.TodoAppTheme
import com.powersync.DatabaseDriverFactory
import com.powersync.PowerSyncDatabase
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch


class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            TodoAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    TodoScreen()
                }
            }
        }
    }
}

@RequiresApi(Build.VERSION_CODES.O)
@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun TodoScreen() {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // Khởi tạo database và repository
    val driverFactory = DatabaseDriverFactory(context)
    val database = remember {
        PowerSyncDatabase(
            factory = driverFactory,
            schema = AppSchema,
            dbFilename = "powersync.db"
        )
    }

    val repository = remember { TodoRepository(database) }

    // State cho UI
    var inputText by remember { mutableStateOf("") }
    var currentListId by remember { mutableStateOf<Int?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // Theo dõi danh sách todos
    val todos by remember(currentListId) {
        if (currentListId != null) {
            repository.watchTodosByListId(currentListId!!)
        } else {
            flowOf(emptyList<Todo>())
        }
    }.collectAsState(initial = emptyList())

    // Khởi tạo database và tạo danh sách mặc định
    LaunchedEffect(Unit) {
        try {
            database.connect(connector = MyConnector())
            currentListId = repository.createDefaultListIfNeeded()
            isLoading = false
        } catch (e: Exception) {
            errorMessage = "Lỗi kết nối database: ${e.message}"
            isLoading = false
        }
    }

    if (isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    } else if (errorMessage != null) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = errorMessage!!,
                    color = MaterialTheme.colorScheme.error,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = {
                        errorMessage = null
                        isLoading = true
                        coroutineScope.launch {
                            try {
                                database.connect(connector = MyConnector())
                                currentListId = repository.createDefaultListIfNeeded()
                                isLoading = false
                            } catch (e: Exception) {
                                errorMessage = "Lỗi kết nối database: ${e.message}"
                                isLoading = false
                            }
                        }
                    }
                ) {
                    Text("Thử lại")
                }
            }
        }
    } else {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Tiêu đề
            Text(
                text = "📝 Todo List",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp)
            )

            // TextField và Button để thêm công việc
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                shape = RoundedCornerShape(12.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = inputText,
                        onValueChange = { inputText = it },
                        modifier = Modifier.weight(1f),
                        placeholder = { Text("Nhập công việc mới...") },
                        shape = RoundedCornerShape(8.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    FloatingActionButton(
                        onClick = {
                            if (inputText.isNotBlank() && currentListId != null) {
                                coroutineScope.launch {
                                    try {
                                        repository.insertTodo(currentListId!!, inputText.trim())
                                        inputText = ""
                                    } catch (e: Exception) {
                                        errorMessage = "Lỗi thêm công việc: ${e.message}"
                                    }
                                }
                            }
                        },
                        modifier = Modifier.size(56.dp)
                    ) {
                        Icon(Icons.Default.Add, contentDescription = "Thêm")
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Danh sách công việc
            if (todos.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "🎉 Chưa có công việc nào!\nHãy thêm công việc đầu tiên.",
                        textAlign = TextAlign.Center,
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(todos) { todo ->
                        TodoItem(
                            todo = todo,
                            onToggleComplete = { completed ->
                                coroutineScope.launch {
                                    try {
                                        repository.updateTodoCompleted(todo.id, if (completed) 1 else 0)
                                    } catch (e: Exception) {
                                        errorMessage = "Lỗi cập nhật: ${e.message}"
                                    }
                                }
                            },
                            onDelete = {
                                coroutineScope.launch {
                                    try {
                                        repository.deleteTodo(todo.id)
                                    } catch (e: Exception) {
                                        errorMessage = "Lỗi xóa: ${e.message}"
                                    }
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TodoItem(
    todo: Todo,
    onToggleComplete: (Boolean) -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (todo.completed == 1)
                MaterialTheme.colorScheme.surfaceVariant
            else
                MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Checkbox để đánh dấu hoàn thành
            Checkbox(
                checked = todo.completed == 1,
                onCheckedChange = onToggleComplete,
                modifier = Modifier.padding(end = 12.dp)
            )

            // Nội dung todo
            Text(
                text = todo.description,
                fontSize = 16.sp,
                modifier = Modifier.weight(1f),
                textDecoration = if (todo.completed == 1)
                    TextDecoration.LineThrough
                else
                    TextDecoration.None,
                color = if (todo.completed == 1)
                    MaterialTheme.colorScheme.onSurfaceVariant
                else
                    MaterialTheme.colorScheme.onSurface
            )

            // Nút xóa
            IconButton(
                onClick = onDelete,
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Icon(
                    Icons.Default.Delete,
                    contentDescription = "Xóa",
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

