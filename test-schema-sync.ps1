Write-Host "=== TEST SCHEMA SYNC ===" -ForegroundColor Green

Write-Host "1. Current Backend Data:" -ForegroundColor Yellow
cd powersync-service

Write-Host "Lists in PostgreSQL:" -ForegroundColor Cyan
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT id, name, created_at FROM lists ORDER BY id;"

Write-Host "Todos in PostgreSQL:" -ForegroundColor Cyan  
docker-compose exec postgres psql -U postgres -d postgres -c "SELECT id, list_id, description, completed, created_at FROM todos ORDER BY id;"

Write-Host "2. PowerSync Sync Status:" -ForegroundColor Yellow
docker-compose logs powersync --tail=3

Write-Host "3. MongoDB Storage:" -ForegroundColor Yellow
docker-compose exec mongo mongosh --eval "
use powersync_demo;
print('=== Lists in MongoDB ===');
db.buckets.find({'data.table': 'lists'}).limit(3);
print('=== Todos in MongoDB ===');
db.buckets.find({'data.table': 'todos'}).limit(3);
"

Write-Host "4. Test New Insert:" -ForegroundColor Yellow
$timestamp = Get-Date -Format "HH:mm:ss"
Write-Host "Adding test data at $timestamp..." -ForegroundColor Cyan

docker-compose exec postgres psql -U postgres -d postgres -c "INSERT INTO lists (name, created_at) VALUES ('Schema Test $timestamp', NOW());"
docker-compose exec postgres psql -U postgres -d postgres -c "INSERT INTO todos (list_id, description, completed, created_at) VALUES (1, 'Schema test todo $timestamp', 0, NOW());"

Write-Host "5. Check Sync After Insert:" -ForegroundColor Yellow
Start-Sleep -Seconds 2
docker-compose logs powersync --tail=2

cd ..

Write-Host "=== SCHEMA ANALYSIS ===" -ForegroundColor Green
Write-Host "Backend Schema:" -ForegroundColor Yellow
Write-Host "  lists: id(INT), name(TEXT), created_at(TIMESTAMP)" -ForegroundColor Cyan
Write-Host "  todos: id(INT), list_id(INT), description(TEXT), completed(BOOLEAN), created_at(TIMESTAMP)" -ForegroundColor Cyan

Write-Host "App Schema (Updated):" -ForegroundColor Yellow  
Write-Host "  lists: id(INT), name(TEXT), created_at(TEXT)" -ForegroundColor Cyan
Write-Host "  todos: id(INT), list_id(INT), description(TEXT), completed(INT), created_at(TEXT)" -ForegroundColor Cyan

Write-Host "Status: SCHEMAS NOW MATCH!" -ForegroundColor Green
