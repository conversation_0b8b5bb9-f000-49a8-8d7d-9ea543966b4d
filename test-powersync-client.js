// Test client để kiểm tra kết nối PowerSync
const WebSocket = require('ws');
const https = require('https');
const http = require('http');

const POWERSYNC_URL = 'http://localhost:8080';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImRldiJ9.eyJzdWIiOiJ1c2VyMTIzIiwiaWF0IjoxNzQ5NTUzMTg4LCJleHAiOjE3NDk2Mzk1ODgsImF1ZCI6WyJwb3dlcnN5bmMtZGV2IiwicG93ZXJzeW5jIl0sImlzcyI6InBvd2Vyc3luYy1kZXYifQ.ZduxpCaP3grTxRW0-v0aJfCknM4fDv1kO3TXqqQ6wOw';

console.log('=== TESTING POWERSYNC CONNECTION ===');

// Test 1: Kiểm tra các endpoints có sẵn
async function testEndpoints() {
    console.log('\n1. Testing HTTP endpoints...');
    
    const endpoints = [
        '/',
        '/api',
        '/api/sync',
        '/api/buckets',
        '/sync',
        '/websocket'
    ];
    
    for (const endpoint of endpoints) {
        try {
            const url = `${POWERSYNC_URL}${endpoint}`;
            console.log(`Testing: ${url}`);
            
            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${JWT_TOKEN}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log(`  Status: ${response.status} ${response.statusText}`);
            
            if (response.status !== 404) {
                const text = await response.text();
                console.log(`  Response: ${text.substring(0, 100)}...`);
            }
        } catch (error) {
            console.log(`  Error: ${error.message}`);
        }
    }
}

// Test 2: Thử kết nối WebSocket
function testWebSocket() {
    console.log('\n2. Testing WebSocket connection...');
    
    const wsUrl = POWERSYNC_URL.replace('http', 'ws') + '/websocket';
    console.log(`WebSocket URL: ${wsUrl}`);
    
    try {
        const ws = new WebSocket(wsUrl, {
            headers: {
                'Authorization': `Bearer ${JWT_TOKEN}`
            }
        });
        
        ws.on('open', () => {
            console.log('✓ WebSocket connected successfully!');
            
            // Gửi message test
            ws.send(JSON.stringify({
                type: 'sync_request',
                data: {}
            }));
        });
        
        ws.on('message', (data) => {
            console.log('✓ Received message:', data.toString());
        });
        
        ws.on('error', (error) => {
            console.log('✗ WebSocket error:', error.message);
        });
        
        ws.on('close', () => {
            console.log('WebSocket connection closed');
        });
        
        // Đóng connection sau 5 giây
        setTimeout(() => {
            ws.close();
        }, 5000);
        
    } catch (error) {
        console.log('✗ WebSocket connection failed:', error.message);
    }
}

// Test 3: Kiểm tra dữ liệu trong MongoDB
async function testMongoData() {
    console.log('\n3. Checking MongoDB data...');
    
    // Sử dụng docker exec để kiểm tra MongoDB
    const { exec } = require('child_process');
    
    exec('cd powersync-service && docker-compose exec mongo mongosh --eval "use powersync_demo; db.buckets.find().limit(3);"', (error, stdout, stderr) => {
        if (error) {
            console.log('✗ MongoDB check failed:', error.message);
            return;
        }
        
        console.log('✓ MongoDB data:');
        console.log(stdout);
    });
}

// Chạy tất cả tests
async function runTests() {
    try {
        await testEndpoints();
        testWebSocket();
        
        setTimeout(() => {
            testMongoData();
        }, 2000);
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

// Polyfill fetch cho Node.js cũ
if (typeof fetch === 'undefined') {
    global.fetch = async (url, options = {}) => {
        return new Promise((resolve, reject) => {
            const lib = url.startsWith('https') ? https : http;
            const urlObj = new URL(url);
            
            const req = lib.request({
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: options.headers || {}
            }, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    resolve({
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        text: () => Promise.resolve(data)
                    });
                });
            });
            
            req.on('error', reject);
            
            if (options.body) {
                req.write(options.body);
            }
            
            req.end();
        });
    };
}

runTests();
